// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from 'vscode';
import { AICompletionService } from './services/aiCompletionService';
import { InlineCompletionProvider } from './providers/inlineCompletionProvider';
import { Logger } from './utils/logger';


class ChatViewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'ai-chat-view';
    private _view?: vscode.WebviewView;

    resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;
        webviewView.webview.options = {
            enableScripts: true,
        };
        webviewView.webview.html = this._getHtmlForWebview();
    }

    private _getHtmlForWebview() {
        // 在这里构建您的聊天界面HTML
        // 为了安全，应该使用vscode-webview-ui-toolkit
        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>AI Chat</title>
            </head>
            <body>
                <h1>AI Assistant Chat</h1>
                <textarea id="prompt-input" rows="4" style="width: 95%;"></textarea>
                <br/>
                <button id="ask-button">Ask AI</button>
                <hr>
                <div id="response-area"></div>
            </body>
            </html>
        `;
    }
}

// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
export function activate(context: vscode.ExtensionContext) {

	// Use the console to output diagnostic information (console.log) and errors (console.error)
	// This line of code will only be executed once when your extension is activated
	Logger.info('扩展 "ai-extension-demo" 已激活!');

	// The command has been defined in the package.json file
	// Now provide the implementation of the command with registerCommand
	// The commandId parameter must match the command field in package.json
	const disposable = vscode.commands.registerCommand('ai-extension-demo.helloWorld', () => {
		// The code you place here will be executed every time your command is executed
		// Display a message box to the user
		vscode.window.showInformationMessage('Hello World from ai-extension-demo!');
	});

	context.subscriptions.push(disposable);

	// 初始化服务和提供器
	Logger.info('正在初始化 AI 补全服务...');
	const aiService = new AICompletionService({
		maxPromptLength: 1000,
		timeout: 5000,
		simulatedDelay: 50
	});

	Logger.info('正在初始化内联补全提供器...');
	const inlineProvider = new InlineCompletionProvider(aiService, {
		minTextLength: 3,
		enabled: true,
		supportedLanguages: ['*'] // 支持所有语言
	});

	// 注册内联补全提供器
	Logger.info('正在注册内联补全提供器...');
    const inlineCompletionItemDisposable = vscode.languages.registerInlineCompletionItemProvider(
        { scheme: 'file' }, // 使用正确的文档选择器
        inlineProvider
    );

    context.subscriptions.push(inlineCompletionItemDisposable);
    Logger.info('内联补全提供器注册完成');
	const provider = new ChatViewProvider();
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider(ChatViewProvider.viewType, provider)
    );
}

// This method is called when your extension is deactivated
export function deactivate() {}
