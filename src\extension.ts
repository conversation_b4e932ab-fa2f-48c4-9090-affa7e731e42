// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from 'vscode';

// 模拟调用AI API的函数
async function getAICompletion(prompt: string): Promise<string> {
    // 在真实应用中，这里将是调用OpenAI或Gemini API的网络请求
    console.log(`向AI发送的Prompt: ${prompt}`);
    // 模拟网络延迟和AI思考时间
    await new Promise(resolve => setTimeout(resolve, 50)); 
    // 模拟AI返回的补全代码
    return `\n\tconsole.log("Hello from AI!");\n}`;
}

const provider: vscode.InlineCompletionItemProvider = {
    async provideInlineCompletionItems(document, position, context, token) {
        // 获取光标前的文本作为AI的Prompt
        const textBeforeCursor = document.getText(
            new vscode.Range(new vscode.Position(0, 0), position)
        );

        // 如果文本太短，则不触发补全
        if (textBeforeCursor.trim().length < 10) {
            return;
        }

        // 调用模拟的AI函数
        const completion = await getAICompletion(textBeforeCursor);
        if (token.isCancellationRequested) {
            return;
        }

        // 创建补全项
        const item = new vscode.InlineCompletionItem(
            completion,
            // 定义补全文本插入的位置范围
            new vscode.Range(position, position.translate(0, completion.length))
        );

        return [item];
    },
};

// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
export function activate(context: vscode.ExtensionContext) {

	// Use the console to output diagnostic information (console.log) and errors (console.error)
	// This line of code will only be executed once when your extension is activated
	console.log('Congratulations, your extension "ai-extension-demo" is now active!');

	// The command has been defined in the package.json file
	// Now provide the implementation of the command with registerCommand
	// The commandId parameter must match the command field in package.json
	const disposable = vscode.commands.registerCommand('ai-extension-demo.helloWorld', () => {
		// The code you place here will be executed every time your command is executed
		// Display a message box to the user
		vscode.window.showInformationMessage('Hello World from ai-extension-demo!');
	});

	context.subscriptions.push(disposable);

	// 注册内联补全提供器
    const InlineCompletionItemDisposable = vscode.languages.registerInlineCompletionItemProvider({ pattern: '**' }, provider);

    context.subscriptions.push(InlineCompletionItemDisposable);
}

// This method is called when your extension is deactivated
export function deactivate() {}
